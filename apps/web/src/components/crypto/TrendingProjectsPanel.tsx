"use client"

import { <PERSON><PERSON> } from "../ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "../ui/card"
import { type JSX, useState } from "react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select"
import { RefreshCw, TrendingUp, TrendingDown, Minus, ExternalLink } from "lucide-react"
import { Skeleton } from "../ui/skeleton"
import Link from "next/link"
import { cn } from "../../lib/utils"
import { trpc } from "../../utils/trpc"

interface TrendingProject {
  name: string
  slug: string
  symbol?: string
  sector?: string
  mindshare?: number
  changePercent?: number
  smartEngagementPoints?: number
  trending?: boolean
  twitterUrl?: string
  websiteUrl?: string
  description?: string
}

interface Sector {
  id: string
  slug: string
  name: string
}

interface TrendingProjectsPanelProps {
  className?: string
}

export function TrendingProjectsPanel({
  className,
}: TrendingProjectsPanelProps): JSX.Element {
  const [selectedSector, setSelectedSector] = useState<string>("")
  const [timeframe, setTimeframe] = useState<"_7Days" | "_30Days">("_7Days")

  // Fetch sectors
  const { data: sectorsData } = trpc.crypto.getSectors.useQuery(undefined, {
    staleTime: 60 * 60 * 1000, // 1 hour
  })
  
  const sectors: Sector[] = sectorsData?.data || []

  // Fetch trending projects
  const {
    data: trendingData,
    isLoading,
    refetch,
    isFetching: isRefetching,
  } = trpc.crypto.getTrendingProjects.useQuery({
    sectorSlug: selectedSector || undefined,
    timeframe: timeframe,
    limit: 10,
  }, {
    staleTime: 15 * 60 * 1000, // 15 minutes
    refetchInterval: 30 * 60 * 1000, // 30 minutes
  })

  const projects: TrendingProject[] = trendingData?.data || []



  const getMindshareColor = (mindshare: number | undefined) => {
    if (!mindshare) return "text-app-headline/40"
    if (mindshare > 80) return "text-green-600"
    if (mindshare > 60) return "text-blue-600"
    if (mindshare > 40) return "text-yellow-600"
    return "text-red-600"
  }

  const getChangeIcon = (change: number | undefined) => {
    if (!change) return <Minus className="h-4 w-4 text-app-headline/30" />
    if (change > 0) return <TrendingUp className="h-4 w-4 text-green-600" />
    return <TrendingDown className="h-4 w-4 text-red-600" />
  }

  const getChangeColor = (change: number | undefined) => {
    if (!change) return "text-app-headline/50"
    return change > 0 ? "text-green-600" : "text-red-600"
  }

  const getSectorBadgeColor = (sector?: string) => {
    if (!sector || sector === 'unknown') return "bg-app-main/10 text-app-headline/70"
    
    const colorMap: Record<string, string> = {
      defi: "bg-blue-500/10 text-blue-700",
      nft: "bg-purple-500/10 text-purple-700",
      gaming: "bg-pink-500/10 text-pink-700",
      infrastructure: "bg-green-500/10 text-green-700",
      dao: "bg-yellow-500/10 text-yellow-700",
      metaverse: "bg-indigo-500/10 text-indigo-700",
      layer1: "bg-emerald-500/10 text-emerald-700",
      layer2: "bg-cyan-500/10 text-cyan-700",
      default: "bg-app-main/10 text-app-headline/70",
    }
    
    return colorMap[sector.toLowerCase()] || colorMap.default
  }

  const handleProjectClick = (project: TrendingProject) => {
    // Redirect to Cookie.fun token page
    const tokenName = project.name.toLowerCase().replace(/\s+/g, '-')
    const cookieUrl = `https://www.cookie.fun/tokens/${tokenName}`
    window.open(cookieUrl, '_blank', 'noopener,noreferrer')
  }



  return (
    <Card className={cn("h-full bg-app-card border-app-stroke shadow-lg", className)}>
      <CardHeader className="bg-app-card border-b border-app-stroke/20">
        <div className="space-y-4">
          {/* Title Row */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <CardTitle className="text-xl sm:text-2xl font-bold text-app-headline">Trending Projects</CardTitle>
          </div>

          {/* Filters Row */}
          <div className="flex flex-col gap-3">
            {/* Time Period Selector and Refresh Button Row */}
            <div className="flex items-center gap-3">
              {/* Time Period Selector */}
              <div className="flex items-center gap-1 rounded-lg bg-app-main/20 p-1 border border-app-stroke/20 flex-1 sm:flex-initial">
                <Button
                  variant={timeframe === "_7Days" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setTimeframe("_7Days")}
                  className={cn(
                    "h-9 px-3 text-app-headline flex-1 sm:flex-initial",
                    timeframe === "_7Days" && "bg-app-main text-app-card"
                  )}
                >
                  7 Days
                </Button>
                <Button
                  variant={timeframe === "_30Days" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setTimeframe("_30Days")}
                  className={cn(
                    "h-9 px-3 text-app-headline flex-1 sm:flex-initial",
                    timeframe === "_30Days" && "bg-app-main text-app-card"
                  )}
                >
                  30 Days
                </Button>
              </div>

              {/* Refresh Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
                disabled={isRefetching}
                className="h-9 transition-all duration-200 border-app-stroke text-app-headline hover:bg-app-main/20 flex-shrink-0"
              >
                <RefreshCw className={cn("h-4 w-4 transition-transform duration-500", isRefetching && "animate-spin")} />
              </Button>
            </div>

            {/* Sector Filter Row */}
            <div className="flex sm:hidden">
              <Select value={selectedSector || "all"} onValueChange={(value) => setSelectedSector(value === "all" ? "" : value)}>
                <SelectTrigger className="!h-9 w-full bg-app-card border-app-stroke text-app-headline">
                  <SelectValue placeholder="All Sectors" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sectors</SelectItem>
                  {sectors.map((sector: Sector) => (
                    <SelectItem key={sector.id} value={sector.slug}>
                      {sector.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Desktop Sector Filter */}
            <div className="hidden sm:flex sm:items-center sm:gap-3">
              <Select value={selectedSector || "all"} onValueChange={(value) => setSelectedSector(value === "all" ? "" : value)}>
                <SelectTrigger className="!h-9 w-[180px] bg-app-card border-app-stroke text-app-headline">
                  <SelectValue placeholder="All Sectors" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sectors</SelectItem>
                  {sectors.map((sector: Sector) => (
                    <SelectItem key={sector.id} value={sector.slug}>
                      {sector.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="bg-app-card">
        <div className="space-y-3 animate-in fade-in duration-500">
          {isLoading ? (
            // Loading skeletons
            Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="rounded-lg border border-app-stroke/30 bg-app-card p-4">
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <Skeleton className="h-5 w-32 bg-app-main/20" />
                      <Skeleton className="h-4 w-24 bg-app-main/10" />
                    </div>
                    <Skeleton className="h-6 w-16 bg-app-main/20" />
                  </div>
                  <Skeleton className="h-4 w-full bg-app-main/10" />
                  <div className="flex items-center gap-4">
                    <Skeleton className="h-4 w-20 bg-app-main/10" />
                    <Skeleton className="h-4 w-20 bg-app-main/10" />
                  </div>
                </div>
              </div>
            ))
          ) : projects.length === 0 ? (
            // Empty state
            <div className="py-12 text-center">
              <p className="text-app-headline/60">
                No trending projects found for the selected criteria.
              </p>
            </div>
          ) : (
            // Project list
            projects.map((project: TrendingProject, index: number) => (
              <div
                key={project.slug}
                className="group relative rounded-lg border border-app-stroke/30 bg-app-card p-4 transition-all duration-200 hover:shadow-lg hover:border-app-main/40 hover:bg-app-card cursor-pointer"
                onClick={() => handleProjectClick(project)}
              >
                <div className="space-y-3">
                  {/* Project Header */}
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center gap-3">
                        <span className="text-lg font-semibold text-app-main">
                          {index + 1}
                        </span>
                        <h3 className="text-lg font-semibold text-app-headline">{project.name}</h3>
                        {project.symbol && (
                          <span className="rounded bg-app-main/20 px-2 py-1 text-xs font-medium text-app-headline">
                            {project.symbol}
                          </span>
                        )}
                        {project.changePercent !== undefined && (
                          <div className={cn("flex items-center gap-1", getChangeColor(project.changePercent))}>
                            {getChangeIcon(project.changePercent)}
                            <span className="text-sm font-medium">
                              {Math.abs(project.changePercent).toFixed(0)}%
                            </span>
                          </div>
                        )}
                      </div>
                      {project.sector && project.sector !== 'unknown' && (
                        <span className={cn("inline-block rounded-full px-2 py-1 text-xs", getSectorBadgeColor(project.sector))}>
                          {project.sector}
                        </span>
                      )}
                    </div>

                    {/* Mindshare Score */}
                    <div className="text-right">
                      <div className={cn("text-2xl font-bold", getMindshareColor(project.mindshare))}>
                        {project.mindshare?.toFixed(1) || "0.0"}
                      </div>
                      <p className="text-xs text-app-headline/50">mindshare</p>
                    </div>
                  </div>

                  {/* Description */}
                  {project.description && (
                    <p className="text-sm text-app-headline/60 line-clamp-2">
                      {project.description}
                    </p>
                  )}

                  {/* Footer with engagement and links */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 text-sm text-app-headline/50">
                      {project.smartEngagementPoints !== undefined && project.smartEngagementPoints > 0 && (
                        <span>
                          {project.smartEngagementPoints.toLocaleString()} engagement points
                        </span>
                      )}
                      {project.trending && (
                        <span className="flex items-center gap-1 text-green-600">
                          <TrendingUp className="h-3 w-3" />
                          Trending
                        </span>
                      )}
                      <Button
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleProjectClick(project)}
                        className="text-xs text-app-main hover:text-app-highlight hover:bg-app-main/10 transition-colors"
                      >
                        View on Cookie.fun
                      </Button>
                    </div>

                    {/* External Links */}
                    <div className="flex items-center gap-2">
                      {project.twitterUrl && (
                        <Link
                          href={project.twitterUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-app-headline/50 transition-colors hover:text-app-highlight"
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Link>
                      )}
                      {project.websiteUrl && (
                        <Link
                          href={project.websiteUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-app-headline/50 transition-colors hover:text-app-highlight"
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}